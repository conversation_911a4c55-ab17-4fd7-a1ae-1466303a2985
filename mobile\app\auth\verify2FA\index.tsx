import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput
} from 'react-native';
import { router } from 'expo-router';
import tw from 'twrnc';
import { useAuth } from '../../../hooks/useAuth';
import { ApiError } from '../../../types/auth';
import AppText from '@/components/ui/Text';
import AppButton from '@/components/ui/Button';
import OTPTextInput from "react-native-otp-textinput";
import { PRIMARY_COLOR } from '@/constants/colors';

export default function Verify2FAScreen() {
  const [totpCode, setTotpCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<TextInput>(null);

  const { verify2FA, requiresTwoFactor } = useAuth();

  useEffect(() => {
    const timer = setTimeout(() => {
      inputRef.current?.focus();
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!requiresTwoFactor) {
      router.replace('/auth/login');
    }
  }, [requiresTwoFactor]);

  const handleVerify = async () => {
    if (!totpCode.trim() || totpCode.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit code');
      return;
    }

    setIsLoading(true);
    try {
      await verify2FA(totpCode.trim());
      router.replace('/main');

    } catch (error) {
      const apiError = error as ApiError;
      Alert.alert(
        'Verification Error',
        apiError.message || 'An error occurred during verification'
      );
      setTotpCode('');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    router.back();
  };


  return (
    <KeyboardAvoidingView
      style={tw`flex-1 bg-white`}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={tw`h-full`}
        keyboardShouldPersistTaps="handled"
      >
        <View style={tw`px-5 flex h-full gap-8`}>
          <View style={tw`flex flex-col gap-12`}>
            <View style={tw`flex gap-2`}>
              <AppText weight='bold' style={tw`text-2xl text-gray-600`}>Verification Code</AppText>
              <AppText style={tw``}>
                Enter your 6-digit verification code from your authenticator app
              </AppText>
            </View>
          </View>
          <View style={tw`flex flex-col gap-4`}>
            <OTPTextInput
              inputCount={6}
              tintColor={PRIMARY_COLOR}
              offTintColor={'#E5E7EB'}
              handleTextChange={setTotpCode}

            />
            <AppButton title="Verify" onPress={handleVerify} loading={isLoading} disabled={isLoading} />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
