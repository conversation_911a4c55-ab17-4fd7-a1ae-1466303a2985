import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { router } from 'expo-router';
import tw from 'twrnc';
import { useAuth } from '../../../hooks/useAuth';
import { ApiError } from '../../../types/auth';

export default function Verify2FAScreen() {
  const [totpCode, setTotpCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<TextInput>(null);

  const { verify2FA, requiresTwoFactor } = useAuth();

  // Auto-focus input when screen loads
  useEffect(() => {
    const timer = setTimeout(() => {
      inputRef.current?.focus();
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  // Redirect if 2FA is not required
  useEffect(() => {
    if (!requiresTwoFactor) {
      router.replace('/auth/login');
    }
  }, [requiresTwoFactor]);

  const handleVerify = async () => {
    if (!totpCode.trim() || totpCode.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit code');
      return;
    }

    setIsLoading(true);
    try {
      const response = await verify2FA(totpCode.trim());

      if (response.success) {
        // 2FA verification successful, navigate to main app
        router.replace('/main');
      } else {
        Alert.alert('Verification Failed', response.message || 'Invalid code');
        setTotpCode(''); // Clear the input
      }
    } catch (error) {
      const apiError = error as ApiError;
      Alert.alert(
        'Verification Error',
        apiError.message || 'An error occurred during verification'
      );
      setTotpCode(''); // Clear the input
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    router.back();
  };

  const formatTotpCode = (text: string) => {
    // Only allow numbers and limit to 6 digits
    const cleaned = text.replace(/[^0-9]/g, '').slice(0, 6);
    setTotpCode(cleaned);
  };

  return (
    <KeyboardAvoidingView
      style={tw`flex-1 bg-white`}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={tw`flex-grow justify-center px-8`}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header */}
        <View style={tw`items-center mb-12`}>
          <View style={tw`w-24 h-24 bg-green-600 rounded-full mb-6 justify-center items-center`}>
            <Text style={tw`text-white text-3xl`}>🔐</Text>
          </View>
          <Text style={tw`text-2xl font-bold text-gray-800 mb-2`}>
            Two-Factor Authentication
          </Text>
          <Text style={tw`text-gray-600 text-center leading-6`}>
            Enter the 6-digit code from your authenticator app to complete the login process
          </Text>
        </View>

        {/* 2FA Form */}
        <View style={tw`mb-8`}>
          {/* TOTP Code Input */}
          <View style={tw`mb-6`}>
            <Text style={tw`text-gray-700 text-sm font-medium mb-3 text-center`}>
              Authentication Code
            </Text>
            <TextInput
              ref={inputRef}
              style={tw`border-2 border-gray-300 rounded-lg px-4 py-4 text-2xl text-center font-mono tracking-widest bg-gray-50`}
              placeholder="000000"
              value={totpCode}
              onChangeText={formatTotpCode}
              keyboardType="numeric"
              maxLength={6}
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
              selectTextOnFocus
            />
            <Text style={tw`text-gray-500 text-xs text-center mt-2`}>
              {totpCode.length}/6 digits
            </Text>
          </View>

          {/* Verify Button */}
          <TouchableOpacity
            style={tw`bg-green-600 py-4 rounded-lg mb-4 ${isLoading || totpCode.length !== 6 ? 'opacity-50' : ''}`}
            onPress={handleVerify}
            disabled={isLoading || totpCode.length !== 6}
            activeOpacity={0.8}
          >
            {isLoading ? (
              <View style={tw`flex-row justify-center items-center`}>
                <ActivityIndicator color="white" size="small" />
                <Text style={tw`text-white text-base font-semibold ml-2`}>
                  Verifying...
                </Text>
              </View>
            ) : (
              <Text style={tw`text-white text-base font-semibold text-center`}>
                Verify Code
              </Text>
            )}
          </TouchableOpacity>

          {/* Back to Login Button */}
          <TouchableOpacity
            style={tw`border border-gray-300 py-4 rounded-lg`}
            onPress={handleBackToLogin}
            disabled={isLoading}
            activeOpacity={0.8}
          >
            <Text style={tw`text-gray-700 text-base font-medium text-center`}>
              Back to Login
            </Text>
          </TouchableOpacity>
        </View>

        {/* Help Text */}
        <View style={tw`items-center`}>
          <Text style={tw`text-gray-500 text-sm text-center mb-2`}>
            Can't access your authenticator app?
          </Text>
          <TouchableOpacity>
            <Text style={tw`text-blue-600 text-sm font-medium`}>
              Contact Support
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
