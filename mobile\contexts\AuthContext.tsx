import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { AuthContextType, AuthState, User, LoginResponse, Verify2FAResponse } from '../types/auth';
import authService from '../services/authService';

const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: true,
  user: null,
  tempToken: null,
  requiresTwoFactor: false,
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, setState] = useState<AuthState>(initialState);

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));

      const requiresTwoFactor = await authService.requiresTwoFactor();
      if (requiresTwoFactor) {
        setState(prev => ({
          ...prev,
          requiresTwoFactor: true,
          tempToken: 'temp',
          isLoading: false
        }));
        return;
      }

      const isAuthenticated = await authService.isAuthenticated();
      if (isAuthenticated) {
        try {
          const user = await authService.getCurrentUser();
          setState(prev => ({
            ...prev,
            isAuthenticated: true,
            user,
            isLoading: false
          }));
        } catch (error) {
          await authService.clearStoredData();
          setState(prev => ({
            ...prev,
            isAuthenticated: false,
            user: null,
            isLoading: false
          }));
        }
      } else {
        setState(prev => ({
          ...prev,
          isAuthenticated: false,
          user: null,
          isLoading: false
        }));
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      setState(prev => ({
        ...prev,
        isAuthenticated: false,
        user: null,
        isLoading: false
      }));
    }
  };

  const login = async (email: string, password: string): Promise<LoginResponse> => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));

      const response = await authService.login({ email, password });
      if (response.requires2FA) {
        setState(prev => ({
          ...prev,
          requiresTwoFactor: true,
          tempToken: response.tempToken || null,
          isLoading: false
        }));
      } else if (response.user) {
        setState(prev => ({
          ...prev,
          isAuthenticated: true,
          user: response.user || null,
          requiresTwoFactor: false,
          tempToken: null,
          isLoading: false
        }));
      }


      return response;
    } catch (error) {
      setState(prev => ({ ...prev, isLoading: false }));
      throw error;
    }
  };

  const verify2FA = async (totpToken: string): Promise<Verify2FAResponse> => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));

      const response = await authService.verify2FA(totpToken);

      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        user: response.user,
        requiresTwoFactor: false,
        tempToken: null,
        isLoading: false
      }));


      return response;
    } catch (error) {
      setState(prev => ({ ...prev, isLoading: false }));
      throw error;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        tempToken: null,
        requiresTwoFactor: false,
      });
    }
  };

  const refreshAuth = async (): Promise<void> => {
    try {
      const user = await authService.getCurrentUser();
      setState(prev => ({
        ...prev,
        user,
        isAuthenticated: true
      }));
    } catch (error) {
      console.error('Refresh auth error:', error);
      await logout();
    }
  };

  const contextValue: AuthContextType = {
    ...state,
    login,
    verify2FA,
    logout,
    refreshAuth,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
