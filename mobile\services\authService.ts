import api from '../utils/axios';
import * as SecureStore from 'expo-secure-store';
import {
  LoginRequest,
  LoginResponse,
  Verify2FARequest,
  Verify2FAResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  User,
  ApiError
} from '../types/auth';

class AuthService {
  private static readonly ACCESS_TOKEN_KEY = 'accessToken';
  private static readonly REFRESH_TOKEN_KEY = 'refreshToken';
  private static readonly TEMP_TOKEN_KEY = 'tempToken';
  private static readonly USER_KEY = 'user';

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await api.post('/auth/login', credentials);
      const data: LoginResponse = response.data;

      if (data.requires2FA && data.tempToken) {
        // Store temp token for 2FA verification
        await SecureStore.setItemAsync(AuthService.TEMP_TOKEN_KEY, data.tempToken);
      } else if (data.accessToken && data.refreshToken && data.user) {
        await this.storeTokens(data.accessToken, data.refreshToken);
        await this.storeUser(data.user);
      }

      return data;
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  async verify2FA(totpToken: string): Promise<Verify2FAResponse> {
    try {
      const tempToken = await SecureStore.getItemAsync(AuthService.TEMP_TOKEN_KEY);
      if (!tempToken) {
        throw new Error('No temporary token found. Please login again.');
      }

      const request: Verify2FARequest = {
        tempToken,
        totpToken
      };

      const response = await api.post('/auth/verify-2fa', request);
      const data: Verify2FAResponse = response.data;

      await this.storeTokens(data.accessToken, data.refreshToken);
      await this.storeUser(data.user);
      await SecureStore.deleteItemAsync(AuthService.TEMP_TOKEN_KEY);

      return data;
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  async refreshToken(): Promise<RefreshTokenResponse> {
    try {
      const refreshToken = await SecureStore.getItemAsync(AuthService.REFRESH_TOKEN_KEY);
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const request: RefreshTokenRequest = { refreshToken };
      const response = await api.post('/auth/refresh', request);
      const data: RefreshTokenResponse = response.data;

      await this.storeTokens(data.accessToken, data.refreshToken);

      return data;
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  async getCurrentUser(): Promise<User> {
    try {
      const response = await api.get('/auth/me');
      const user: User = response.data;
      await this.storeUser(user);
      return user;
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.warn('Server logout failed:', error);
    } finally {
      await this.clearStoredData();
    }
  }

  async storeTokens(accessToken: string, refreshToken: string): Promise<void> {
    await Promise.all([
      SecureStore.setItemAsync(AuthService.ACCESS_TOKEN_KEY, accessToken),
      SecureStore.setItemAsync(AuthService.REFRESH_TOKEN_KEY, refreshToken)
    ]);
  }

  async getAccessToken(): Promise<string | null> {
    return await SecureStore.getItemAsync(AuthService.ACCESS_TOKEN_KEY);
  }

  async getRefreshToken(): Promise<string | null> {
    return await SecureStore.getItemAsync(AuthService.REFRESH_TOKEN_KEY);
  }

  async getTempToken(): Promise<string | null> {
    return await SecureStore.getItemAsync(AuthService.TEMP_TOKEN_KEY);
  }

  async storeUser(user: User): Promise<void> {
    await SecureStore.setItemAsync(AuthService.USER_KEY, JSON.stringify(user));
  }

  async getStoredUser(): Promise<User | null> {
    try {
      const userJson = await SecureStore.getItemAsync(AuthService.USER_KEY);
      return userJson ? JSON.parse(userJson) : null;
    } catch (error) {
      console.error('Error parsing stored user:', error);
      return null;
    }
  }

  async clearStoredData(): Promise<void> {
    await Promise.all([
      SecureStore.deleteItemAsync(AuthService.ACCESS_TOKEN_KEY),
      SecureStore.deleteItemAsync(AuthService.REFRESH_TOKEN_KEY),
      SecureStore.deleteItemAsync(AuthService.TEMP_TOKEN_KEY),
      SecureStore.deleteItemAsync(AuthService.USER_KEY)
    ]);
  }

  async isAuthenticated(): Promise<boolean> {
    const accessToken = await this.getAccessToken();
    return !!accessToken;
  }

  async requiresTwoFactor(): Promise<boolean> {
    const tempToken = await this.getTempToken();
    return !!tempToken;
  }

  private handleApiError(error: any): ApiError {
    if (error.response) {
      return {
        message: error.response.data?.message || 'An error occurred',
        status: error.response.status,
        code: error.response.data?.code
      };
    } else if (error.request) {
      return {
        message: 'Network error. Please check your connection.',
        code: 'NETWORK_ERROR'
      };
    } else {
      return {
        message: error.message || 'An unexpected error occurred',
        code: 'UNKNOWN_ERROR'
      };
    }
  }
}

export default new AuthService();
