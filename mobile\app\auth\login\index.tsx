import AppButton from '@/components/ui/Button';
import AppText from '@/components/ui/Text';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  View
} from 'react-native';
import tw from 'twrnc';
import { useAuth } from '../../../hooks/useAuth';
import { ApiError } from '../../../types/auth';
import { TextInput } from 'react-native-paper';
import { AxiosError } from 'axios';

export default function LoginScreen() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const { login } = useAuth();

  const handleLogin = async () => {
    if (!username.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter both username and password');
      return;
    }

    setIsLoading(true);
    try {
      const response = await login(username.trim(), password);

      if (response.refreshToken) {
        router.push('/auth/verify2FA');
      } else {
        router.replace('/main');
      }

    } catch (error: unknown) {
      const apiError = error as AxiosError<ApiError>;

      if (apiError.response?.status === 401) {
        Alert.alert('Login Failed', 'Invalid credentials');
        return;
      }

      Alert.alert(
        'Login Error',
        apiError.message || 'An error occurred during login'
      );
      console.error('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={tw`flex-1 bg-white`}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={tw`h-full`}
        keyboardShouldPersistTaps="handled"
      >
        <View style={tw`px-5 flex justify-between h-full gap-8`}>
          <View style={tw`flex flex-col gap-12`}>
            <View style={tw`flex gap-2`}>
              <AppText weight='bold' style={tw`text-2xl text-gray-600`}>Sign In</AppText>
              <AppText style={tw``}>
                Fill in your credentials to login in to WASH System
              </AppText>
            </View>
            <View style={tw`flex flex-col gap-4`}>
              <View>
                <AppText weight='medium' style={tw`text-gray-600`}>Email </AppText>
                <TextInput
                  label="Email"
                  value={username}
                  onChangeText={setUsername}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!isLoading}
                  keyboardType='email-address'
                  mode='outlined'
                  left={<TextInput.Icon color={'#9CA3AF'} icon="email" />}
                  style={tw`bg-white`}
                  outlineColor='#E5E7EB'

                />

              </View>
              <View>
                <AppText weight='medium' style={tw`text-gray-600`}>Password </AppText>
                <TextInput
                  label="*********"
                  value={password}
                  onChangeText={setPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!isLoading}
                  secureTextEntry={!showPassword}
                  mode='outlined'
                  left={<TextInput.Icon color={'#9CA3AF'} icon="lock" />}
                  right={
                    !showPassword ? (
                      <TextInput.Icon color={'#9CA3AF'} icon="eye-off" onPress={() => setShowPassword(!showPassword)} />
                    ) : (
                      <TextInput.Icon color={'#9CA3AF'} icon="eye" onPress={() => setShowPassword(!showPassword)} />
                    )
                  }
                  style={tw`bg-white`}
                  outlineColor='#E5E7EB'
                />
              </View>
            </View>
          </View>
          <View style={tw`flex flex-col gap-4`}>
            <AppText weight='medium' style={tw`text-center`}>
              By submitting this application you confirm that you are authorized to share this information and agree with our
              <AppText weight='medium' style={tw`text-[#2078FF]`}> Terms and Conditions</AppText>
            </AppText>
            <AppButton title="Login" onPress={handleLogin} loading={isLoading} disabled={isLoading} />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
