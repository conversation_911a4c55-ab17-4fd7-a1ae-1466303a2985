export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  telephoneNumber: string;
  role: {
    name: string;
    code: string;
    privileges: string[];
  };
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  accessToken?: string;
  refreshToken?: string;
  tempToken?: string;
  user?: User;
  requires2FA?: boolean;
}

export interface Verify2FARequest {
  tempToken: string;
  totpToken: string;
}

export interface Verify2FAResponse {
  accessToken: string;
  refreshToken: string;
  user: User;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  tempToken: string | null;
  requiresTwoFactor: boolean;
}

export interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<LoginResponse>;
  verify2FA: (totpToken: string) => Promise<Verify2FAResponse>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}
